/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * instrumentation.ts
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

/**
 * Next.js instrumentation file for server-side error tracking
 * This file provides hooks for capturing server-side errors with PostHog
 */

export function register() {
  // No-op for initialization
  // This function is called when the instrumentation is registered
}

export const onRequestError = async (
  err: Error,
  request: Request,
  context: { routerKind?: string; routePath?: string }
) => {
  // Only capture errors in Node.js runtime (not Edge runtime)
  // This ensures PostHog works correctly since it requires Node.js APIs
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    try {
      // Dynamic import to avoid issues with Edge runtime
      const { getPostHogServer } = await import('./lib/posthog-server')
      const posthog = getPostHogServer()
      
      if (!posthog) {
        // Fallback to console logging if PostHog is not configured
        console.error('Server request error (PostHog not configured):', {
          error: err.message,
          stack: err.stack,
          url: request.url,
          method: request.method,
          routerKind: context.routerKind,
          routePath: context.routePath,
        })
        return
      }

      // Extract distinct_id from PostHog cookie if available
      let distinctId: string | undefined

      if (request.headers.get('cookie')) {
        const cookieString = request.headers.get('cookie')!
        const postHogCookieMatch = cookieString.match(/ph_phc_.*?_posthog=([^;]+)/)
        
        if (postHogCookieMatch && postHogCookieMatch[1]) {
          try {
            const decodedCookie = decodeURIComponent(postHogCookieMatch[1])
            const postHogData = JSON.parse(decodedCookie)
            distinctId = postHogData.distinct_id
          } catch (cookieError) {
            console.error('Error parsing PostHog cookie:', cookieError)
          }
        }
      }

      // Capture the exception with PostHog
      await posthog.captureException(err, {
        distinct_id: distinctId,
        source: 'server-request-error',
        url: request.url,
        method: request.method,
        routerKind: context.routerKind,
        routePath: context.routePath,
        userAgent: request.headers.get('user-agent'),
        timestamp: new Date().toISOString(),
      })
    } catch (captureError) {
      // Fallback to console logging if PostHog capture fails
      console.error('Failed to capture server error with PostHog:', captureError)
      console.error('Original server error:', {
        error: err.message,
        stack: err.stack,
        url: request.url,
        method: request.method,
      })
    }
  }
}
