/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * posthog-server.ts
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

import { PostHog } from 'posthog-node'

let posthogInstance: PostHog | null = null

/**
 * Get PostHog server instance as singleton
 * Used for server-side error tracking and analytics
 */
export function getPostHogServer(): PostHog | null {
  // Return null if PostHog is not configured
  if (!process.env.NEXT_PUBLIC_POSTHOG_KEY || !process.env.NEXT_PUBLIC_POSTHOG_HOST) {
    return null
  }

  if (!posthogInstance) {
    posthogInstance = new PostHog(
      process.env.NEXT_PUBLIC_POSTHOG_KEY,
      {
        host: process.env.NEXT_PUBLIC_POSTHOG_HOST,
        flushAt: 1,
        flushInterval: 0,
      }
    )
  }
  
  return posthogInstance
}

/**
 * Capture exception on server-side
 * @param error - The error to capture
 * @param distinctId - Optional user identifier
 * @param additionalProperties - Additional properties to include
 */
export async function captureServerException(
  error: Error,
  distinctId?: string,
  additionalProperties?: Record<string, any>
) {
  const posthog = getPostHogServer()
  
  if (!posthog) {
    // Fallback to console logging if PostHog is not available
    console.error('Server error (PostHog not configured):', error)
    return
  }

  try {
    await posthog.captureException(error, {
      distinct_id: distinctId,
      source: 'server',
      ...additionalProperties,
    })
  } catch (captureError) {
    // Fallback to console logging if PostHog capture fails
    console.error('Failed to capture exception with PostHog:', captureError)
    console.error('Original error:', error)
  }
}
