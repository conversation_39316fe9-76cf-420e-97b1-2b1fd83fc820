/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * instrumentation-client.ts
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

import posthog from 'posthog-js'

/**
 * Client-side PostHog initialization for Next.js 15.3+
 * This provides a lightweight setup for PostHog with error tracking enabled
 */

// Only initialize if PostHog key is available
if (process.env.NEXT_PUBLIC_POSTHOG_KEY) {
  posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY, {
    api_host: process.env.NEXT_PUBLIC_POSTHOG_HOST || '/ingest',
    ui_host: 'https://us.posthog.com',
    person_profiles: 'identified_only',
    capture_pageview: false, // We handle pageviews manually
    disable_session_recording: false,
    advanced_disable_toolbar_metrics: true,
    opt_in_site_apps: true,
    autocapture: false,
    // Enable exception autocapture for error tracking
    capture_exceptions: true,
    loaded: (posthog) => {
      if (process.env.NODE_ENV === 'development') {
        posthog.debug()
      }
    },
  })
}
