/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * posthog-client.ts
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

'use client'

import posthog from 'posthog-js'

/**
 * Client-side error capture utility for PostHog
 * This function provides a consistent way to capture exceptions on the client-side
 */
export function captureClientException(
  error: Error,
  additionalProperties?: Record<string, any>
) {
  // Check if PostHog is initialized and available
  if (!posthog || typeof posthog.captureException !== 'function') {
    // Fallback to console logging if PostHog is not available
    console.error('Client error (PostHog not available):', error)
    return
  }

  try {
    posthog.captureException(error, {
      source: 'client',
      timestamp: new Date().toISOString(),
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
      ...additionalProperties,
    })
  } catch (captureError) {
    // Fallback to console logging if PostHog capture fails
    console.error('Failed to capture exception with PostHog:', captureError)
    console.error('Original error:', error)
  }
}

/**
 * Capture a custom error event with additional context
 * Useful for handled errors that you want to track
 */
export function captureClientError(
  message: string,
  context?: Record<string, any>
) {
  const error = new Error(message)
  captureClientException(error, {
    ...context,
    errorType: 'handled',
  })
}

/**
 * Wrap an async function to automatically capture any thrown errors
 * Usage: const safeFunction = withErrorCapture(myAsyncFunction, { component: 'MyComponent' })
 */
export function withErrorCapture<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  context?: Record<string, any>
): T {
  return (async (...args: Parameters<T>) => {
    try {
      return await fn(...args)
    } catch (error) {
      captureClientException(error as Error, {
        ...context,
        functionName: fn.name,
        arguments: args,
      })
      throw error // Re-throw the error to maintain original behavior
    }
  }) as T
}

/**
 * React hook for capturing errors in components
 * Returns a function that can be used to capture errors with component context
 */
export function useErrorCapture(componentName?: string) {
  return (error: Error, additionalContext?: Record<string, any>) => {
    captureClientException(error, {
      component: componentName,
      ...additionalContext,
    })
  }
}
