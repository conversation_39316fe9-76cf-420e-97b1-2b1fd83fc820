/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * error.tsx
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

'use client'

import { useEffect } from 'react'
import posthog from 'posthog-js'
import ErrorBoundary from "../../../web/components/ui/error"

// biome-ignore lint/suspicious/noShadowRestrictedNames: Error is a valid parameter name for error boundaries
export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Capture exception with PostHog for error tracking
    if (posthog && process.env.NEXT_PUBLIC_POSTHOG_KEY) {
      posthog.captureException(error, {
        source: 'route-error-boundary',
        digest: error.digest,
      })
    }
  }, [error])

  return (
    <ErrorBoundary
      description="Sorry, something went wrong with the application."
      className="min-h-svh"
      error={error}
    />
  )
}
